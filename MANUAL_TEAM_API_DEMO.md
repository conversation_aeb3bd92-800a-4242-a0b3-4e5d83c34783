# 🏆 Manual Team Management API - Demo Guide

## 📋 **Tổng quan**

Hệ thống quản lý team thủ công đã được implement thành công với các tính năng:

- ✅ **Create Manual Team**: Tạo team mới không có trong API Football
- ✅ **Update Manual Team**: <PERSON><PERSON><PERSON> nhật thông tin team thủ công
- ✅ **Delete Manual Team**: Xóa team thủ công (với kiểm tra references)
- ✅ **Get Manual Teams**: Lấy danh sách team thủ công với pagination
- ✅ **Authentication & Authorization**: Role-based access control
- ✅ **Validation & Error Handling**: Comprehensive validation
- ✅ **Unit Tests**: 8/8 tests passed
- ✅ **Swagger Documentation**: Complete API docs

## 🔐 **Authentication Requirements**

### **System User Authentication**
```bash
# Login để lấy JWT token
POST /auth/system/login
{
  "username": "admin",
  "password": "your_password"
}

# Response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "...",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin"
  }
}
```

### **Role Requirements**
- **Create/Update Team**: Editor+ role (Editor, Admin)
- **Delete Team**: Admin role only
- **Get Teams**: Public (no authentication)

## 🚀 **API Endpoints**

### **1. Create Manual Team**
```bash
POST /football/teams/manual
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "Saigon FC",
  "code": "SGN",
  "country": "Vietnam",
  "founded": 2020,
  "national": false,
  "description": "Professional football club in Ho Chi Minh City",
  "venue": {
    "name": "Thong Nhat Stadium",
    "city": "Ho Chi Minh City",
    "capacity": 15000,
    "surface": "grass"
  }
}
```

**Response (201):**
```json
{
  "data": {
    "id": 1001,
    "externalId": null,
    "name": "Saigon FC",
    "code": "SGN",
    "country": "Vietnam",
    "founded": 2020,
    "national": false,
    "isManual": true,
    "description": "Professional football club in Ho Chi Minh City",
    "venue": {
      "name": "Thong Nhat Stadium",
      "city": "Ho Chi Minh City",
      "capacity": 15000,
      "surface": "grass"
    },
    "createdBy": 1,
    "updatedBy": 1
  },
  "status": 201
}
```

### **2. Update Manual Team**
```bash
PUT /football/teams/manual/1001
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "Saigon FC Updated",
  "description": "Updated description for Saigon FC"
}
```

### **3. Delete Manual Team**
```bash
DELETE /football/teams/manual/1001
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "message": "Team \"Saigon FC\" has been successfully deleted",
  "status": 200
}
```

### **4. Get Manual Teams**
```bash
GET /football/teams/manual?page=1&limit=10&search=Saigon&country=Vietnam
```

**Response (200):**
```json
{
  "data": [
    {
      "id": 1001,
      "externalId": null,
      "name": "Saigon FC",
      "code": "SGN",
      "country": "Vietnam",
      "isManual": true
    }
  ],
  "meta": {
    "totalItems": 1,
    "totalPages": 1,
    "currentPage": 1,
    "limit": 10
  },
  "status": 200
}
```

## 🛡️ **Error Handling**

### **Validation Errors (400)**
```json
{
  "message": [
    "name must be longer than or equal to 2 characters",
    "country must be longer than or equal to 2 characters"
  ],
  "error": "Bad Request",
  "statusCode": 400
}
```

### **Conflict Error (409)**
```json
{
  "message": "Team with name \"Saigon FC\" already exists",
  "error": "Conflict",
  "statusCode": 409
}
```

### **Authorization Error (403)**
```json
{
  "message": "Insufficient permissions. Editor+ role required.",
  "error": "Forbidden",
  "statusCode": 403
}
```

## 🧪 **Testing**

### **Unit Tests**
```bash
npm test -- --testPathPattern=team.service.spec.ts
```

**Results:**
- ✅ 8/8 tests passed
- ✅ Create manual team functionality
- ✅ Update manual team functionality  
- ✅ Delete manual team functionality
- ✅ Error handling scenarios

### **Integration Testing**
```bash
# Start the application
npm run start:api:dev

# Test endpoints with Postman or curl
curl -X GET http://localhost:3000/football/teams/manual
```

## 📊 **Database Schema**

### **Teams Table Updates**
```sql
-- New columns added to teams table
ALTER TABLE teams ADD COLUMN isManual BOOLEAN DEFAULT FALSE;
ALTER TABLE teams ADD COLUMN description VARCHAR(500);
ALTER TABLE teams ADD COLUMN createdBy INTEGER;
ALTER TABLE teams ADD COLUMN updatedBy INTEGER;
ALTER TABLE teams ALTER COLUMN externalId DROP NOT NULL;

-- Indexes
CREATE INDEX idx_team_is_manual ON teams(isManual);
```

## 🔄 **Integration với hệ thống hiện tại**

### **Compatibility**
- ✅ Không ảnh hưởng đến API teams hiện tại
- ✅ Manual teams có `externalId = null`
- ✅ API teams có `isManual = false`
- ✅ Fixture service đã được update để handle nullable externalId

### **Cache Management**
- ✅ Auto clear cache khi create/update/delete manual teams
- ✅ Separate cache keys cho manual teams

## 🎯 **Next Steps**

1. **Deploy to staging environment**
2. **Create admin interface for team management**
3. **Add bulk import functionality**
4. **Implement team logo upload**
5. **Add team statistics for manual teams**

## 📝 **Notes**

- Manual teams không thể được sync từ API Football
- Chỉ có thể delete manual teams nếu không có fixture references
- Team name phải unique across toàn bộ hệ thống
- Audit trail được maintain với createdBy/updatedBy fields
