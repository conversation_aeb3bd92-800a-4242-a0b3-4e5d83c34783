import { IsInt, IsString, IsOptional, Min, Max, IsBoolean, IsObject, IsIn, Length, IsUrl, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/** DTO for querying teams */
export class GetTeamsDto {
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    league?: number;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    season?: number;

    @IsString()
    @IsOptional()
    country?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    page: number = 1;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    @IsOptional()
    limit: number = 10;

    @IsString()
    @IsOptional()
    search?: string;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    newdb?: boolean;
}

export class GetTeamLeaguesAndSeasonsDto {
    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    newdb?: boolean;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    includeHistorical?: boolean;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    currentSeasonOnly?: boolean;

    @IsString()
    @IsOptional()
    @IsIn(['by-league', 'by-season'])
    format?: 'by-league' | 'by-season';
}

/** DTO for team response */
export class TeamResponseDto {
    @IsInt()
    id: number;

    @IsInt()
    @IsOptional()
    externalId?: number; // nullable for manual teams

    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    code?: string;

    @IsString()
    @IsOptional()
    country?: string;

    @IsString()
    @IsOptional()
    logo?: string;

    @IsInt()
    @IsOptional()
    season?: number;

    @IsInt()
    @IsOptional()
    leagueId?: number;

    @IsInt()
    @IsOptional()
    founded?: number;

    @IsBoolean()
    national: boolean;

    @IsObject()
    @IsOptional()
    venue?: {
        id?: number;
        name?: string;
        address?: string;
        city?: string;
        capacity?: number;
        surface?: string;
        image?: string;
    };

    @IsBoolean()
    @IsOptional()
    isManual?: boolean; // indicates if team was created manually

    @IsString()
    @IsOptional()
    description?: string; // additional description for manual teams

    @IsInt()
    @IsOptional()
    createdBy?: number; // SystemUser ID who created this manual team

    @IsInt()
    @IsOptional()
    updatedBy?: number; // SystemUser ID who last updated this manual team
}

/** Paginated response for teams */
export interface PaginatedTeamsResponse {
    data: TeamResponseDto[];
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };
    status: number;
}

/** DTO for league participation */
export class LeagueParticipationDto {
    league: {
        id: number;
        externalId: number;
        name: string;
        country: string;
        logo?: string;
        type: string;
    };
    seasons: number[];
    isCurrentlyActive: boolean;
}

/** DTO for season participation */
export class SeasonParticipationDto {
    season: number;
    leagues: Array<{
        id: number;
        externalId: number;
        name: string;
        country: string;
        logo?: string;
        type: string;
    }>;
    isCurrentSeason: boolean;
}

/** DTO for team leagues and seasons response */
export class TeamLeaguesAndSeasonsDto {
    team: {
        id: number;
        externalId: number;
        name: string;
        country?: string;
        logo?: string;
    };
    participations: LeagueParticipationDto[];
    seasonParticipations?: SeasonParticipationDto[];
    totalLeagues: number;
    totalSeasons: number;
    currentSeason: number;
    format: 'by-league' | 'by-season';
}

/** DTO for creating a manual team */
export class CreateTeamDto {
    @IsString()
    @Length(2, 100)
    name: string;

    @IsString()
    @IsOptional()
    @Length(2, 10)
    code?: string;

    @IsString()
    @Length(2, 50)
    country: string;

    @IsString()
    @IsOptional()
    @IsUrl()
    logo?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1800)
    @Max(new Date().getFullYear())
    @IsOptional()
    founded?: number;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    national?: boolean;

    @IsObject()
    @IsOptional()
    @ValidateNested()
    @Type(() => VenueDto)
    venue?: VenueDto;

    @IsString()
    @IsOptional()
    @Length(0, 500)
    description?: string;
}

/** DTO for updating a manual team */
export class UpdateTeamDto {
    @IsString()
    @Length(2, 100)
    @IsOptional()
    name?: string;

    @IsString()
    @IsOptional()
    @Length(2, 10)
    code?: string;

    @IsString()
    @Length(2, 50)
    @IsOptional()
    country?: string;

    @IsString()
    @IsOptional()
    @IsUrl()
    logo?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1800)
    @Max(new Date().getFullYear())
    @IsOptional()
    founded?: number;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    national?: boolean;

    @IsObject()
    @IsOptional()
    @ValidateNested()
    @Type(() => VenueDto)
    venue?: VenueDto;

    @IsString()
    @IsOptional()
    @Length(0, 500)
    description?: string;
}

/** DTO for venue information */
export class VenueDto {
    @IsString()
    @IsOptional()
    @Length(2, 100)
    name?: string;

    @IsString()
    @IsOptional()
    @Length(2, 200)
    address?: string;

    @IsString()
    @IsOptional()
    @Length(2, 50)
    city?: string;

    @Type(() => Number)
    @IsInt()
    @Min(100)
    @Max(200000)
    @IsOptional()
    capacity?: number;

    @IsString()
    @IsOptional()
    @IsIn(['grass', 'artificial', 'hybrid'])
    surface?: string;

    @IsString()
    @IsOptional()
    @IsUrl()
    image?: string;
}