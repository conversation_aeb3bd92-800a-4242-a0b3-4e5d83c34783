import { Injectable, Logger, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { Team } from '../models/team.entity';
import { GetTeamsDto, TeamResponseDto, PaginatedTeamsResponse, GetTeamLeaguesAndSeasonsDto, TeamLeaguesAndSeasonsDto, LeagueParticipationDto, SeasonParticipationDto, CreateTeamDto, UpdateTeamDto } from '../models/team.dto';
import { Fixture } from '../models/fixture.entity';
import { CacheService } from '../../../core/cache/cache.service';
import { ImageService } from '../../../shared/services/image.service';

@Injectable()
export class TeamService {
    private readonly logger = new Logger(TeamService.name);

    constructor(
        @InjectRepository(Team)
        private readonly teamRepository: Repository<Team>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly imageService: ImageService,
    ) { }

    async getTeamById(externalId: number, newdb?: boolean): Promise<TeamResponseDto> {
        const cacheKey = `team_detail_${externalId}`;

        // If newdb=true, skip cache and force fetch from API
        if (!newdb) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning team from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }
        }

        let team = await this.teamRepository.findOne({ where: { externalId } });

        // If newdb=true or team not found in DB, fetch from API
        if (newdb || !team) {
            if (newdb) {
                this.logger.debug(`Force fetching team ${externalId} from API (newdb=true)`);
            } else {
                this.logger.debug(`Team ${externalId} not found in DB, fetching from API`);
            }

            const teams = await this.fetchFromApi({ id: externalId } as any);
            if (teams.length === 0) {
                throw new NotFoundException(`Team with externalId ${externalId} not found`);
            }
            team = teams[0];
            try {
                await this.teamRepository.upsert(team, ['externalId']);
                this.logger.debug(`Upserted team ${externalId} to DB`);

                // Clear cache when updating from API
                await this.cacheService.deleteByPattern(`team_detail_${externalId}`);
                this.logger.debug(`Cleared cache for team ${externalId}`);
            } catch (error) {
                this.logger.error(`Failed to upsert team to DB: ${error.message}`);
            }
        }

        const response = this.mapToResponseDto([team])[0];

        // Cache the response (with shorter TTL if forced refresh)
        const cacheTTL = newdb ? 3600 : 604800; // 1 hour vs 1 week
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), cacheTTL);
        this.logger.debug(`Cached team response for key: ${cacheKey} (TTL: ${cacheTTL}s)`);

        return response;
    }
    /**
     * Get teams by query parameters
     * @param query - Query parameters (league, season, country, page, limit, newdb)
     * @returns Paginated list of teams
     */
    async getTeams(query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const searchKey = query.search ?? '';
        const cacheKey = `teams_list_${query.league ?? ''}_${query.season ?? ''}_${query.country ?? ''}_${searchKey}_${page}_${limit}`;

        // If newdb=true, skip cache and force fetch from API
        if (!query.newdb) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning teams from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }
        }

        let { teams, totalItems } = await this.fetchFromDb(query);

        // If newdb=true or no teams found in DB, fetch from API
        if (query.newdb || teams.length === 0) {
            if (query.newdb) {
                this.logger.debug(`Force fetching teams from API (newdb=true) for query: ${JSON.stringify(query)}`);
            } else {
                this.logger.debug(`No teams found in DB for query: ${JSON.stringify(query)}, fetching from API`);
            }

            teams = await this.fetchFromApi(query);
            if (teams.length > 0) {
                try {
                    await this.teamRepository.upsert(teams, ['externalId']);
                    this.logger.debug(`Upserted ${teams.length} teams to DB`);

                    // Clear related cache when updating from API
                    if (query.newdb) {
                        await this.cacheService.deleteByPattern(`teams_list_*`);
                        this.logger.debug(`Cleared teams cache patterns`);
                    }

                    const paginatedResult = await this.fetchFromDb(query);
                    teams = paginatedResult.teams;
                    totalItems = paginatedResult.totalItems;
                } catch (error) {
                    this.logger.error(`Failed to upsert teams: ${error.message}`);
                }
            }
        }

        const response: PaginatedTeamsResponse = {
            data: this.mapToResponseDto(teams),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        if (response.data.length > 0) {
            // Cache with shorter TTL if forced refresh
            const cacheTTL = query.newdb ? 3600 : 604800; // 1 hour vs 1 week
            await this.cacheService.setCache(cacheKey, JSON.stringify(response), cacheTTL);
            this.logger.debug(`Cached paginated response for key: ${cacheKey} (TTL: ${cacheTTL}s)`);
        }
        return response;
    }

    private async fetchFromDb(query: GetTeamsDto): Promise<{ teams: Team[]; totalItems: number }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;

        const qb = this.teamRepository.createQueryBuilder('team');
        if (query.league) {
            qb.andWhere('team.leagueId = :league', { league: query.league });
        }
        if (query.season) {
            qb.andWhere('team.season = :season', { season: query.season });
        }
        if (query.country) {
            qb.andWhere('team.country = :country', { country: query.country.toLowerCase() });
        }
        if (query.search) {
            // Search in team name, country, and code (case-insensitive)
            const searchTerm = `%${query.search.toLowerCase()}%`;
            qb.andWhere(
                '(LOWER(team.name) LIKE :searchTerm OR LOWER(team.country) LIKE :searchTerm OR LOWER(team.code) LIKE :searchTerm)',
                { searchTerm }
            );
        }

        const [teams, totalItems] = await qb
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        this.logger.debug(`Fetched ${teams.length} teams from DB for query: ${JSON.stringify(query)}`);
        return { teams, totalItems };
    }

    private async fetchFromApi(query: GetTeamsDto | { id: number }): Promise<Team[]> {
        try {
            const apiQuery: any = {};
            if ('id' in query) {
                apiQuery.id = query.id;
            } else {
                const { page, limit, newdb, ...rest } = query as GetTeamsDto;
                Object.assign(apiQuery, rest);
                if (apiQuery.country) {
                    apiQuery.country = apiQuery.country.charAt(0).toUpperCase() + apiQuery.country.slice(1).toLowerCase();
                }
            }

            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/teams`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiQuery)}`);
                return axios.get(apiUrl, {
                    params: apiQuery,
                    headers,
                });
            });

            this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}: ${JSON.stringify(response.data)}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No data returned from API for query ${JSON.stringify(apiQuery)}`);
                return [];
            }

            const teams = await Promise.all(
                response.data.response.map(async (apiData: any): Promise<Team | null> => {
                    // Kiểm tra kỹ hơn để tránh lỗi undefined
                    if (!apiData?.team || typeof apiData.team.id !== 'number') {
                        this.logger.warn(`Invalid team data: ${JSON.stringify(apiData)}`);
                        return null;
                    }

                    const logoPath = apiData.team.logo
                        ? await this.imageService.downloadImage(apiData.team.logo, 'teams', `${apiData.team.id}.png`)
                        : '';
                    const imageVenuePath = apiData.venue.image
                        ? await this.imageService.downloadImage(apiData.venue.image, 'venues', `${apiData.team.id}.svg`)
                        : '';
                    const team = this.teamRepository.create({
                        externalId: apiData.team.id,
                        name: apiData.team.name || 'Unknown',
                        code: apiData.team.code || null,
                        country: (apiData.team.country || 'Unknown').toLowerCase(),
                        logo: logoPath,
                        season: ('season' in query ? query.season : 0) || 0,
                        leagueId: ('league' in query ? query.league : null) || null,
                        founded: apiData.team.founded || null,
                        national: apiData.team.national || false,
                        venue: apiData.venue
                            ? {
                                id: apiData.venue.id,
                                name: apiData.venue.name,
                                address: apiData.venue.address,
                                city: apiData.venue.city,
                                capacity: apiData.venue.capacity,
                                surface: apiData.venue.surface,
                                image: imageVenuePath,
                            }
                            : null,
                    } as Team);

                    return team;
                }),
            );

            const validTeams: Team[] = teams.filter((team): team is Team => team !== null);
            this.logger.debug(`Processed ${validTeams.length} valid teams from API`);
            return validTeams;
        } catch (error) {
            this.logger.error(`Failed to fetch from API: ${error.message}`);
            return [];
        }
    }

    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }

    private mapToResponseDto(teams: Team[]): TeamResponseDto[] {
        return teams.map(team => ({
            id: team.id,
            externalId: team.externalId,
            name: team.name,
            code: team.code,
            country: team.country,
            logo: team.logo,
            season: team.season,
            leagueId: team.leagueId,
            founded: team.founded,
            national: team.national,
            venue: team.venue,
            isManual: team.isManual,
            description: team.description,
            createdBy: team.createdBy,
            updatedBy: team.updatedBy,
        }));
    }

    /**
     * Get leagues and seasons that a team has participated in
     * @param teamExternalId - Team external ID
     * @param options - Query options
     * @returns Team leagues and seasons data
     */
    async getTeamLeaguesAndSeasons(
        teamExternalId: number,
        options: GetTeamLeaguesAndSeasonsDto = {}
    ): Promise<TeamLeaguesAndSeasonsDto> {
        const {
            newdb = false,
            includeHistorical = true,
            currentSeasonOnly = false,
            format = 'by-league'
        } = options;

        const cacheKey = `team_leagues_seasons_${teamExternalId}_${JSON.stringify(options)}`;

        // Cache check
        if (!newdb) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning team leagues/seasons from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }
        }

        // Get team basic info
        const team = await this.teamRepository.findOne({ where: { externalId: teamExternalId } });
        if (!team) {
            throw new NotFoundException(`Team with externalId ${teamExternalId} not found`);
        }

        let participations = [];

        // Method 1: Get from fixtures (historical + free)
        if (includeHistorical) {
            participations = await this.getParticipationsFromFixtures(teamExternalId);
            this.logger.debug(`Found ${participations.length} participations from fixtures for team ${teamExternalId}`);
        }

        // Method 2: API for current season (if needed)
        const currentSeason = new Date().getFullYear();

        // If includeHistorical=false, we only get current season from API
        if (!includeHistorical) {
            this.logger.debug(`Getting current season data from API for team ${teamExternalId} (includeHistorical=false)`);
            const currentData = await this.getCurrentSeasonFromAPI(teamExternalId);
            participations = currentData;
        } else {
            // If we have historical data, also try to get comprehensive seasons data from API
            // This ensures we have complete season coverage beyond just fixture data
            const shouldFetchApiData = newdb || !currentSeasonOnly;

            if (shouldFetchApiData) {
                this.logger.debug(`Fetching comprehensive season data from API for team ${teamExternalId}`);
                const apiData = await this.getCurrentSeasonFromAPI(teamExternalId);
                participations = this.mergeParticipations(participations, apiData);
            }
        }

        // Filter if only current season needed
        if (currentSeasonOnly) {
            participations = participations.filter(p => p.season === currentSeason);
        }

        const result = this.formatTeamLeaguesResponse(team, participations, format, currentSeason);

        // Cache with appropriate TTL
        const ttl = currentSeasonOnly ? 3600 : 86400; // 1h vs 24h
        await this.cacheService.setCache(cacheKey, JSON.stringify(result), ttl);

        return result;
    }

    /**
     * Get team participations from fixtures table
     */
    private async getParticipationsFromFixtures(teamExternalId: number): Promise<any[]> {

        const result = await this.fixtureRepository
            .createQueryBuilder('f')
            .leftJoin('leagues', 'l', 'l.externalId = f.leagueId')
            .select([
                'f.season as season',
                'f.leagueId as leagueExternalId',
                'COALESCE(l.id, f.leagueId) as leagueId',
                'COALESCE(l.name, f.leagueName) as leagueName',
                'l.country as leagueCountry',
                'l.logo as leagueLogo',
                'l.type as leagueType'
            ])
            .where('f.homeTeamId = :teamId OR f.awayTeamId = :teamId', { teamId: teamExternalId })
            .groupBy('f.season, f.leagueId, f.leagueName, l.id, l.name, l.country, l.logo, l.type')
            .orderBy('f.season', 'DESC')
            .addOrderBy('COALESCE(l.name, f.leagueName)', 'ASC')
            .getRawMany();

        return result;
    }

    /**
     * Get current season data from API using team seasons endpoint
     */
    private async getCurrentSeasonFromAPI(teamExternalId: number): Promise<any[]> {
        const cacheKey = `team_seasons_${teamExternalId}`;
        const cacheTTL = 6 * 30 * 24 * 3600; // 6 months in seconds

        try {
            // Check cache first
            const cachedData = await this.cacheService.getCache(cacheKey);
            if (cachedData) {
                this.logger.debug(`Returning team seasons from cache for team ${teamExternalId}`);
                return JSON.parse(cachedData);
            }

            this.logger.debug(`Fetching team seasons from API for team ${teamExternalId}`);

            // Call API Football team seasons endpoint using existing pattern
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/teams/seasons`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                const apiParams = { team: teamExternalId };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiParams)}`);
                return axios.get(apiUrl, {
                    params: apiParams,
                    headers,
                });
            });

            if (!response?.data?.response || !Array.isArray(response.data.response)) {
                this.logger.warn(`Invalid API response for team seasons ${teamExternalId}:`, response?.data);
                return [];
            }

            const seasons = response.data.response;
            this.logger.debug(`API response for team ${teamExternalId} seasons:`, {
                seasons: seasons.length,
                data: seasons
            });

            // Process seasons data to match our participation format
            const participations = await this.processTeamSeasonsData(teamExternalId, seasons);

            // Cache the processed data for 6 months
            await this.cacheService.setCache(cacheKey, JSON.stringify(participations), cacheTTL);
            this.logger.debug(`Cached team seasons for team ${teamExternalId} (TTL: ${cacheTTL}s)`);

            return participations;

        } catch (error) {
            this.logger.error(`Failed to fetch team seasons for team ${teamExternalId}:`, error);
            return [];
        }
    }

    /**
     * Process team seasons data from API to match our participation format
     */
    private async processTeamSeasonsData(teamExternalId: number, seasons: number[]): Promise<any[]> {
        const participations = [];

        try {
            // For each season, we need to get the leagues the team participated in
            // Since the API only returns seasons, we need to get league data from fixtures or another source
            for (const season of seasons) {
                // Try to get league data from fixtures first
                const fixtureLeagues = await this.fixtureRepository
                    .createQueryBuilder('f')
                    .leftJoin('leagues', 'l', 'l.externalId = f.leagueId')
                    .select([
                        'f.leagueId as leagueExternalId',
                        'COALESCE(l.id, f.leagueId) as leagueId',
                        'COALESCE(l.name, f.leagueName) as leagueName',
                        'l.country as leagueCountry',
                        'l.logo as leagueLogo',
                        'l.type as leagueType'
                    ])
                    .where('(f.homeTeamId = :teamId OR f.awayTeamId = :teamId) AND f.season = :season',
                        { teamId: teamExternalId, season })
                    .groupBy('f.leagueId, f.leagueName, l.id, l.name, l.country, l.logo, l.type')
                    .getRawMany();

                // If we found leagues from fixtures, use them
                if (fixtureLeagues.length > 0) {
                    for (const league of fixtureLeagues) {
                        participations.push({
                            season: season,
                            leagueexternalid: league.leagueexternalid,
                            leagueid: league.leagueid,
                            leaguename: league.leaguename,
                            leaguecountry: league.leaguecountry,
                            leaguelogo: league.leaguelogo,
                            leaguetype: league.leaguetype
                        });
                    }
                } else {
                    // If no fixture data, create a minimal entry
                    // This could happen for very recent seasons or teams with no fixtures yet
                    this.logger.debug(`No fixture data found for team ${teamExternalId} in season ${season}`);
                    participations.push({
                        season: season,
                        leagueexternalid: null,
                        leagueid: null,
                        leaguename: 'Unknown League',
                        leaguecountry: null,
                        leaguelogo: null,
                        leaguetype: null
                    });
                }
            }

            this.logger.debug(`Processed ${participations.length} participations from ${seasons.length} seasons for team ${teamExternalId}`);
            return participations;

        } catch (error) {
            this.logger.error(`Failed to process team seasons data for team ${teamExternalId}:`, error);
            return [];
        }
    }

    /**
     * Merge participations from different sources
     */
    private mergeParticipations(existing: any[], newData: any[]): any[] {
        const merged = [...existing];

        for (const newItem of newData) {
            const existingIndex = merged.findIndex(
                item => item.season === newItem.season &&
                    item.leagueexternalid === newItem.leagueexternalid
            );

            if (existingIndex === -1) {
                merged.push(newItem);
            } else {
                // Update existing with newer data
                merged[existingIndex] = { ...merged[existingIndex], ...newItem };
            }
        }

        return merged.sort((a, b) => b.season - a.season);
    }

    /**
     * Format response based on requested format
     */
    private formatTeamLeaguesResponse(
        team: Team,
        participations: any[],
        format: 'by-league' | 'by-season',
        currentSeason: number
    ): TeamLeaguesAndSeasonsDto {
        const teamBasic = {
            id: team.id,
            externalId: team.externalId,
            name: team.name,
            country: team.country,
            logo: team.logo
        };

        if (format === 'by-league') {
            // Group by league
            const leagueMap = new Map<number, LeagueParticipationDto>();

            for (const participation of participations) {
                const leagueId = participation.leagueexternalid;

                if (!leagueMap.has(leagueId)) {
                    leagueMap.set(leagueId, {
                        league: {
                            id: participation.leagueid,
                            externalId: participation.leagueexternalid,
                            name: participation.leaguename,
                            country: participation.leaguecountry,
                            logo: participation.leaguelogo,
                            type: participation.leaguetype
                        },
                        seasons: [],
                        isCurrentlyActive: false
                    });
                }

                const leagueData = leagueMap.get(leagueId)!;
                if (!leagueData.seasons.includes(participation.season)) {
                    leagueData.seasons.push(participation.season);
                }

                if (participation.season === currentSeason) {
                    leagueData.isCurrentlyActive = true;
                }
            }

            // Sort seasons within each league
            leagueMap.forEach(league => {
                league.seasons.sort((a, b) => b - a);
            });

            const participationsList = Array.from(leagueMap.values());
            const uniqueSeasons = [...new Set(participations.map(p => p.season))];

            return {
                team: teamBasic,
                participations: participationsList,
                totalLeagues: leagueMap.size,
                totalSeasons: uniqueSeasons.length,
                currentSeason,
                format: 'by-league'
            };
        } else {
            // Group by season
            const seasonMap = new Map<number, SeasonParticipationDto>();

            for (const participation of participations) {
                const season = participation.season;

                if (!seasonMap.has(season)) {
                    seasonMap.set(season, {
                        season,
                        leagues: [],
                        isCurrentSeason: season === currentSeason
                    });
                }

                const seasonData = seasonMap.get(season)!;
                const existingLeague = seasonData.leagues.find(
                    l => l.externalId === participation.leagueexternalid
                );

                if (!existingLeague) {
                    seasonData.leagues.push({
                        id: participation.leagueid,
                        externalId: participation.leagueexternalid,
                        name: participation.leaguename,
                        country: participation.leaguecountry,
                        logo: participation.leaguelogo,
                        type: participation.leaguetype
                    });
                }
            }

            const seasonParticipations = Array.from(seasonMap.values())
                .sort((a, b) => b.season - a.season);

            const uniqueLeagues = new Set(participations.map(p => p.leagueexternalid));

            return {
                team: teamBasic,
                participations: [], // Empty for by-season format
                seasonParticipations,
                totalLeagues: uniqueLeagues.size,
                totalSeasons: seasonMap.size,
                currentSeason,
                format: 'by-season'
            };
        }
    }

    /**
     * Create a manual team (not from API)
     * @param createTeamDto - Team creation data
     * @param createdBy - SystemUser ID who creates the team
     * @returns Created team
     */
    async createManualTeam(createTeamDto: CreateTeamDto, createdBy: number): Promise<TeamResponseDto> {
        this.logger.debug(`Creating manual team: ${createTeamDto.name} by user ${createdBy}`);

        // Check if team name already exists
        const existingTeam = await this.teamRepository.findOne({
            where: { name: createTeamDto.name }
        });

        if (existingTeam) {
            throw new ConflictException(`Team with name "${createTeamDto.name}" already exists`);
        }

        // Create team entity
        const teamData = {
            ...createTeamDto,
            isManual: true,
            externalId: null, // Manual teams don't have external ID
            createdBy,
            updatedBy: createdBy,
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        const team = this.teamRepository.create(teamData);

        // Save to database
        const savedTeam = await this.teamRepository.save(team);
        this.logger.debug(`Manual team created with ID: ${savedTeam.id}`);

        // Clear cache
        await this.cacheService.deleteByPattern('teams_list_*');
        this.logger.debug('Cleared teams cache after manual team creation');

        return this.mapToResponseDto([savedTeam])[0];
    }

    /**
     * Update a manual team
     * @param teamId - Team ID to update
     * @param updateTeamDto - Update data
     * @param updatedBy - SystemUser ID who updates the team
     * @returns Updated team
     */
    async updateManualTeam(teamId: number, updateTeamDto: UpdateTeamDto, updatedBy: number): Promise<TeamResponseDto> {
        this.logger.debug(`Updating manual team ID: ${teamId} by user ${updatedBy}`);

        // Find team
        const team = await this.teamRepository.findOne({ where: { id: teamId } });
        if (!team) {
            throw new NotFoundException(`Team with ID ${teamId} not found`);
        }

        // Check if it's a manual team
        if (!team.isManual) {
            throw new BadRequestException('Cannot update API-sourced teams. Only manual teams can be updated.');
        }

        // Check if name is being changed and already exists
        if (updateTeamDto.name && updateTeamDto.name !== team.name) {
            const existingTeam = await this.teamRepository.findOne({
                where: { name: updateTeamDto.name }
            });
            if (existingTeam && existingTeam.id !== teamId) {
                throw new ConflictException(`Team with name "${updateTeamDto.name}" already exists`);
            }
        }

        // Update team
        Object.assign(team, updateTeamDto, {
            updatedBy,
            updatedAt: new Date(),
        });

        const savedTeam = await this.teamRepository.save(team);
        this.logger.debug(`Manual team updated: ${savedTeam.id}`);

        // Clear cache
        await this.cacheService.deleteByPattern('teams_list_*');
        await this.cacheService.deleteByPattern(`team_detail_${teamId}`);
        this.logger.debug('Cleared teams cache after manual team update');

        return this.mapToResponseDto([savedTeam])[0];
    }

    /**
     * Delete a manual team
     * @param teamId - Team ID to delete
     * @returns Success message
     */
    async deleteManualTeam(teamId: number): Promise<{ message: string }> {
        this.logger.debug(`Deleting manual team ID: ${teamId}`);

        // Find team
        const team = await this.teamRepository.findOne({ where: { id: teamId } });
        if (!team) {
            throw new NotFoundException(`Team with ID ${teamId} not found`);
        }

        // Check if it's a manual team
        if (!team.isManual) {
            throw new BadRequestException('Cannot delete API-sourced teams. Only manual teams can be deleted.');
        }

        // Check if team is referenced in fixtures or other entities
        const fixtureCount = await this.fixtureRepository.count({
            where: [
                { homeTeamId: team.externalId || teamId },
                { awayTeamId: team.externalId || teamId }
            ]
        });

        if (fixtureCount > 0) {
            throw new BadRequestException(`Cannot delete team. It is referenced in ${fixtureCount} fixture(s).`);
        }

        // Delete team
        await this.teamRepository.remove(team);
        this.logger.debug(`Manual team deleted: ${teamId}`);

        // Clear cache
        await this.cacheService.deleteByPattern('teams_list_*');
        await this.cacheService.deleteByPattern(`team_detail_${teamId}`);
        this.logger.debug('Cleared teams cache after manual team deletion');

        return { message: `Team "${team.name}" has been successfully deleted` };
    }

    /**
     * Get all manual teams
     * @param query - Query parameters
     * @returns Paginated list of manual teams
     */
    async getManualTeams(query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const searchKey = query.search ?? '';

        const queryBuilder = this.teamRepository.createQueryBuilder('team')
            .where('team.isManual = :isManual', { isManual: true });

        // Add search filter
        if (searchKey) {
            queryBuilder.andWhere(
                '(LOWER(team.name) LIKE LOWER(:search) OR LOWER(team.country) LIKE LOWER(:search))',
                { search: `%${searchKey}%` }
            );
        }

        // Add country filter
        if (query.country) {
            queryBuilder.andWhere('LOWER(team.country) = LOWER(:country)', { country: query.country });
        }

        // Get total count
        const totalItems = await queryBuilder.getCount();

        // Get paginated results
        const teams = await queryBuilder
            .orderBy('team.createdAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();

        return {
            data: this.mapToResponseDto(teams),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };
    }
}