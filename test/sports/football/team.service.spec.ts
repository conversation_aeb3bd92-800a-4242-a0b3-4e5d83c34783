import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { TeamService } from '../../../src/sports/football/services/team.service';
import { Team } from '../../../src/sports/football/models/team.entity';
import { Fixture } from '../../../src/sports/football/models/fixture.entity';
import { CacheService } from '../../../src/core/cache/cache.service';
import { ImageService } from '../../../src/shared/services/image.service';
import { CreateTeamDto, UpdateTeamDto } from '../../../src/sports/football/models/team.dto';

describe('TeamService - Manual Team Management', () => {
    let service: TeamService;
    let teamRepository: Repository<Team>;
    let fixtureRepository: Repository<Fixture>;
    let cacheService: CacheService;

    const mockTeamRepository = {
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        remove: jest.fn(),
        createQueryBuilder: jest.fn(),
        count: jest.fn(),
    };

    const mockFixtureRepository = {
        count: jest.fn(),
    };

    const mockCacheService = {
        deleteByPattern: jest.fn(),
        setCache: jest.fn(),
        getCache: jest.fn(),
    };

    const mockConfigService = {
        get: jest.fn(),
    };

    const mockImageService = {
        downloadImage: jest.fn(),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TeamService,
                {
                    provide: getRepositoryToken(Team),
                    useValue: mockTeamRepository,
                },
                {
                    provide: getRepositoryToken(Fixture),
                    useValue: mockFixtureRepository,
                },
                {
                    provide: CacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: ConfigService,
                    useValue: mockConfigService,
                },
                {
                    provide: ImageService,
                    useValue: mockImageService,
                },
            ],
        }).compile();

        service = module.get<TeamService>(TeamService);
        teamRepository = module.get<Repository<Team>>(getRepositoryToken(Team));
        fixtureRepository = module.get<Repository<Fixture>>(getRepositoryToken(Fixture));
        cacheService = module.get<CacheService>(CacheService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('createManualTeam', () => {
        const createTeamDto: CreateTeamDto = {
            name: 'Test FC',
            code: 'TFC',
            country: 'Vietnam',
            founded: 2020,
            national: false,
            description: 'Test team description',
        };

        const createdBy = 1;

        it('should create a manual team successfully', async () => {
            const savedTeam = {
                id: 1,
                ...createTeamDto,
                isManual: true,
                externalId: null,
                createdBy,
                updatedBy: createdBy,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            mockTeamRepository.findOne.mockResolvedValue(null);
            mockTeamRepository.create.mockReturnValue(savedTeam);
            mockTeamRepository.save.mockResolvedValue(savedTeam);
            mockCacheService.deleteByPattern.mockResolvedValue(undefined);

            const result = await service.createManualTeam(createTeamDto, createdBy);

            expect(mockTeamRepository.findOne).toHaveBeenCalledWith({
                where: { name: createTeamDto.name }
            });
            expect(mockTeamRepository.create).toHaveBeenCalledWith({
                name: createTeamDto.name,
                code: createTeamDto.code,
                country: createTeamDto.country,
                logo: createTeamDto.logo,
                founded: createTeamDto.founded,
                national: createTeamDto.national || false,
                venue: createTeamDto.venue,
                description: createTeamDto.description,
                isManual: true,
                externalId: undefined,
                createdBy,
                updatedBy: createdBy,
            });
            expect(mockTeamRepository.save).toHaveBeenCalledWith(savedTeam);
            expect(mockCacheService.deleteByPattern).toHaveBeenCalledWith('teams_list_*');
            expect(result.name).toBe(createTeamDto.name);
            expect(result.isManual).toBe(true);
        });

        it('should throw ConflictException if team name already exists', async () => {
            const existingTeam = { id: 1, name: createTeamDto.name };
            mockTeamRepository.findOne.mockResolvedValue(existingTeam);

            await expect(service.createManualTeam(createTeamDto, createdBy))
                .rejects.toThrow(ConflictException);

            expect(mockTeamRepository.findOne).toHaveBeenCalledWith({
                where: { name: createTeamDto.name }
            });
            expect(mockTeamRepository.create).not.toHaveBeenCalled();
        });
    });

    describe('updateManualTeam', () => {
        const teamId = 1;
        const updateTeamDto: UpdateTeamDto = {
            name: 'Updated Test FC',
            description: 'Updated description',
        };
        const updatedBy = 1;

        it('should update a manual team successfully', async () => {
            const existingTeam = {
                id: teamId,
                name: 'Test FC',
                isManual: true,
                createdBy: 1,
            };

            const updatedTeam = {
                ...existingTeam,
                ...updateTeamDto,
                updatedBy,
                updatedAt: new Date(),
            };

            mockTeamRepository.findOne
                .mockResolvedValueOnce(existingTeam) // First call for finding team
                .mockResolvedValueOnce(null); // Second call for checking name uniqueness
            mockTeamRepository.save.mockResolvedValue(updatedTeam);
            mockCacheService.deleteByPattern.mockResolvedValue(undefined);

            const result = await service.updateManualTeam(teamId, updateTeamDto, updatedBy);

            expect(mockTeamRepository.findOne).toHaveBeenCalledWith({ where: { id: teamId } });
            expect(mockTeamRepository.save).toHaveBeenCalled();
            expect(mockCacheService.deleteByPattern).toHaveBeenCalledWith('teams_list_*');
            expect(mockCacheService.deleteByPattern).toHaveBeenCalledWith(`team_detail_${teamId}`);
            expect(result.name).toBe(updateTeamDto.name);
        });

        it('should throw NotFoundException if team not found', async () => {
            mockTeamRepository.findOne.mockResolvedValue(null);

            await expect(service.updateManualTeam(teamId, updateTeamDto, updatedBy))
                .rejects.toThrow(NotFoundException);
        });

        it('should throw BadRequestException if team is not manual', async () => {
            const apiTeam = {
                id: teamId,
                name: 'API Team',
                isManual: false,
            };

            mockTeamRepository.findOne.mockResolvedValue(apiTeam);

            await expect(service.updateManualTeam(teamId, updateTeamDto, updatedBy))
                .rejects.toThrow(BadRequestException);
        });
    });

    describe('deleteManualTeam', () => {
        const teamId = 1;

        it('should delete a manual team successfully', async () => {
            const manualTeam = {
                id: teamId,
                name: 'Test FC',
                isManual: true,
                externalId: null,
            };

            mockTeamRepository.findOne.mockResolvedValue(manualTeam);
            mockFixtureRepository.count.mockResolvedValue(0);
            mockTeamRepository.remove.mockResolvedValue(manualTeam);
            mockCacheService.deleteByPattern.mockResolvedValue(undefined);

            const result = await service.deleteManualTeam(teamId);

            expect(mockTeamRepository.findOne).toHaveBeenCalledWith({ where: { id: teamId } });
            expect(mockFixtureRepository.count).toHaveBeenCalled();
            expect(mockTeamRepository.remove).toHaveBeenCalledWith(manualTeam);
            expect(result.message).toContain('Test FC');
        });

        it('should throw BadRequestException if team has fixture references', async () => {
            const manualTeam = {
                id: teamId,
                name: 'Test FC',
                isManual: true,
            };

            mockTeamRepository.findOne.mockResolvedValue(manualTeam);
            mockFixtureRepository.count.mockResolvedValue(5); // Has 5 fixtures

            await expect(service.deleteManualTeam(teamId))
                .rejects.toThrow(BadRequestException);

            expect(mockTeamRepository.remove).not.toHaveBeenCalled();
        });

        it('should throw BadRequestException if team is not manual', async () => {
            const apiTeam = {
                id: teamId,
                name: 'API Team',
                isManual: false,
            };

            mockTeamRepository.findOne.mockResolvedValue(apiTeam);

            await expect(service.deleteManualTeam(teamId))
                .rejects.toThrow(BadRequestException);
        });
    });
});
